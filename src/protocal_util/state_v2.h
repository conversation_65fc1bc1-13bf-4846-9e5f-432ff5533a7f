/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 车辆运行状态v2协议类
 *        
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/

#pragma once
#include "state_v1.h"
namespace cvis_bcl
{
    
    class StateV2:public StateV1
    {
    public:
    StateV2();
    virtual ~StateV2();
    virtual bool ParseProtocal(BYTE* buffer,int length){};
        //组装心跳报文,发送完成后需要调用Free()函数，释放申请的内存
    BYTE* MakeStateV2Package(StateV2Data data,DWORD& length);
    void MakePureStateV2Package(BYTE* buffer,DWORD& offset,StateV2Data data);
    };

}