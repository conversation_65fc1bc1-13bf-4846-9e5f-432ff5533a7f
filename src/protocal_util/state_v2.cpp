#include "state_v2.h"
#include <string.h>
using namespace cvis_bcl;

StateV2::StateV2()
{
    msg_type = "V2C_STATE_V2";
}

StateV2::~StateV2()
{

}


BYTE* StateV2::MakeStateV2Package(StateV2Data data,DWORD& length)
{
    HeaderData header = MakeHeader(msg_type,data.GetLength());
    length = data.GetLength()+sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    uint32_t offset =0;
    header_.MakeHeader(buffer_,offset,header);
    MakePureDataPackage(buffer_,offset,data.v1_data);
    MakePureStateV2Package(buffer_,offset,data);
    return buffer_;
}

void StateV2::MakePureStateV2Package(BYTE* buffer,DWORD& offset,StateV2Data data)
{
    MakeUnsignedChar(buffer,offset,data.tap_pos);
    MakeUnsignedChar(buffer,offset,data.drive_engine_type);
    MakeUnsignedShort(buffer,offset,MakeWORD(data.acc_pedal_pos,0.1,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.velocity_can,0.01,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.enigne_speed,1,0));
    MakeUnsignedInt(buffer,offset,MakeDWORD(data.engine_torque,0.01,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.motor_speed,1,-20000));
    MakeUnsignedInt(buffer,offset,MakeDWORD(data.motor_torque,0.01,-500000));
    MakeUnsignedChar(buffer,offset,data.parking_brake_flag);
    MakeUnsignedChar(buffer,offset,data.brake_flag);
    MakeUnsignedShort(buffer,offset,MakeWORD(data.brake_pedal_pos,0.1,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.brake_pressure,0.01,0));
    for(int i=0;i<5;i++)
    {
        MakeUnsignedChar(buffer,offset,data.wheel_brake[i]);
    }
    MakeUnsignedInt(buffer,offset,MakeDWORD(data.steering_angle,1e-4,-10000000));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.steering_angle_speed,0.01,-10000));
    MakeUnsignedInt(buffer,offset,MakeDWORD(data.mileage_total,0.1,0));
    MakeUnsignedInt(buffer,offset,MakeDWORD(data.mileage_since_start,0.1,0));
    MakeUnsignedInt(buffer,offset,MakeDWORD(data.mileage_since_serviced,0.1,0));
    MakeUnsignedInt(buffer,offset,MakeDWORD(data.driving_range,1,0));
    MakeUnsignedChar(buffer,offset,data.head_lights);
    MakeUnsignedChar(buffer,offset,data.near_or_far);
    MakeUnsignedChar(buffer,offset,data.left_turn_lights);
    MakeUnsignedChar(buffer,offset,data.right_turn_lights);
    MakeUnsignedChar(buffer,offset,data.horn);
    MakeUnsignedShort(buffer,offset,MakeWORD(data.consumption_fule,0.01,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.consumption_ave_fule,0.01,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.consumption_ave_fule_since_served,0.01,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.sot,0.1,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.batt_vol,0.1,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.batt_cur,0.01,-2000));
    MakeUnsignedChar(buffer,offset,MakeBYTE(data.batt_max_temper,1,-100));
    MakeUnsignedChar(buffer,offset,data.charge_state);
    MakeUnsignedShort(buffer,offset,MakeWORD(data.charge_voltage,0.1,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.charge_current,0.01,-2000));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.comsumption_power,0.01,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.comsumption_ave,0.01,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.comsumption_ave_since_start,0.01,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.soc,0.01,0));
    MakeUnsignedChar(buffer,offset,data.abs_flag);
    MakeUnsignedChar(buffer,offset,data.ebd_flag);
    MakeUnsignedChar(buffer,offset,data.vdc_flag);
    MakeUnsignedChar(buffer,offset,data.tcs_flag);
    MakeUnsignedChar(buffer,offset,data.ebs_flag);
    MakeUnsignedChar(buffer,offset,data.esp_flag);
    MakeUnsignedChar(buffer,offset,data.fcw_flag);
    MakeUnsignedChar(buffer,offset,data.fca_flag);
    MakeUnsignedChar(buffer,offset,data.aeb_flag);
    MakeUnsignedChar(buffer,offset,data.ldw_flag);
    MakeUnsignedChar(buffer,offset,data.lka_flag);
    MakeUnsignedChar(buffer,offset,data.cc_flag);
    MakeUnsignedChar(buffer,offset,data.acc_flag);
    MakeUnsignedChar(buffer,offset,data.pcc_flag);
    MakeUnsignedChar(buffer,offset,data.pacc_flag);
    MakeUnsignedChar(buffer,offset,data.lcc_flag);
    MakeUnsignedChar(buffer,offset,data.lca_flag);
    MakeUnsignedChar(buffer,offset,data.dms_flag);
    MakeUnsignedChar(buffer,offset,data.daw_flag);
    MakeUnsignedShort(buffer,offset,MakeWORD(data.cc_setting_velocity,0.01,0));
    MakeUnsignedShort(buffer,offset,MakeWORD(data.xcc_target_velocity,0.01,0));
    
}