#include "state_v1.h"
#include <string.h>
using namespace cvis_bcl;

StateV1::StateV1()
{
    msg_type = "V2C_STATE_V1";
}

StateV1::~StateV1()
{

}

BYTE* StateV1::MakeStateV1Package(StateV1Data data,DWORD& length)
{
    StateV1Data_ inner_data;
    HeaderData header = MakeHeader(msg_type,sizeof(inner_data));
    length = sizeof(inner_data)+sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    uint32_t offset =0;
    header_.MakeHeader(buffer_,offset,header);
    MakePureDataPackage(buffer_,offset,data);
    return buffer_;
}

void StateV1::MakePureDataPackage(BYTE* buffer,DWORD& offset,StateV1Data data)
{
     MakeUnsignedInt(buffer_,offset,data.msg_seq);
    for(int i=0;i<8;i++)
    {
        MakeUnsignedChar(buffer_,offset, data.veh_id[i]);
    }
    MakeUsignedLongLong(buffer_,offset,data.timestamp);
    MakeUsignedLongLong(buffer_,offset,data.timestamp_gnss);
    MakeUnsignedShort(buffer_,offset,MakeWORD(data.velocity_gnss,0.01,-10000));
    MakeUnsignedInt(buffer_,offset,MakeLongitude(data.longitude));
    MakeUnsignedInt(buffer_,offset,MakeLatitude(data.latitude));
    MakeUnsignedInt(buffer_,offset,MakeElevation(data.elevation));
    MakeUnsignedInt(buffer_,offset,MakeHeading(data.heading));
    MakeUnsignedChar(buffer_,offset,data.gnss_status);
}