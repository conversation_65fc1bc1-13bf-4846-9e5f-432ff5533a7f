#include "inh_res.h"

using namespace cvis_bcl;

INHRes::INHRes()
{

}

INHRes::~INHRes()
{

}

bool INHRes::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }
    data_.msg_seq = GetHostUnsignedInt(buffer,offset);
    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] =GetHostUnsignedChar(buffer,offset);
    }
    for(int i=0;i<36;i++)
    {
        data_.uuid[i] = GetHostUnsignedChar(buffer,offset);
    }
    data_.res = GetHostUnsignedChar(buffer,offset);
    return true;
}

INHResData INHRes::GetData()
{
    return data_;
}