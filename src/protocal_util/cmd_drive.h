/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 云端下发车辆驾驶建议协议类
 *       
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class CmdDrive:public ProtocalBase
    {
        public:
        CmdDrive();
        virtual ~CmdDrive();
        virtual bool ParseProtocal(BYTE* buffer,int length);
        void ParseDispatchData(BYTE* buffer,DWORD& offset,RemoteDispatchData& data);
        void ParseOSAData(BYTE* buffer,DWORD& offset,OSAData& data);
        void ParseETOData(BYTE* buffer,DWORD& offset,EmergencyTakeoverData& data);
        void ParseGlobalRoutintgData(BYTE* buffer,DWORD& offset,GlobalRoutingData& data);
        CmdDriveData GetData();
        private:
        CmdDriveData data_;
    };

}