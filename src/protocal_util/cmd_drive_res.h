/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 云端下发车辆驾驶建议指令返回协议类
 *       
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include "protocal_base.h"
namespace cvis_bcl
{
    class CmdDriveRes:public ProtocalBase
    {
        public:
        CmdDriveRes();
        virtual ~CmdDriveRes();
        BYTE* MakeProtocalPackage(CmdDriveResData data,DWORD& length);
        void MakeDispatchPackage(BYTE* buffer,DWORD& offset,RemoteDispatchResData data);
        void MakeOSAPackage(BYTE* buffer,DWORD& offset,OSAResData data);
        void MakeETOPackage(BYTE* buffer,DWORD& offset,EmergencyTakeoverResData data);
        void MakeGlobalRoutingPackage(BYTE* buffer,DWORD& offset,GlobalRoutingResData data);
    };

}