#include "cmd_drive_res.h"
#include "string.h"

using namespace cvis_bcl;


CmdDriveRes::CmdDriveRes()
{
    msg_type="V2C_CMD_DRIVE_RES";
}

CmdDriveRes::~CmdDriveRes()
{

}


BYTE* CmdDriveRes::MakeProtocalPackage(CmdDriveResData data,DWORD& length)
{
    HeaderData header = MakeHeader(msg_type,data.GetLength());
    length = data.GetLength()+sizeof(header);
    buffer_ = (BYTE*)malloc(length);
    memset(buffer_,0,length);
    uint32_t offset =0;
    header_.MakeHeader(buffer_,offset,header);
    MakeUnsignedInt(buffer_,offset,data.msg_seq);
    for(int i=0;i<8;i++)
    {
       MakeUnsignedChar(buffer_,offset,data.veh_id[i]);
    }
    for(int i=0;i<36;i++)
    {
        MakeUnsignedChar(buffer_,offset,data.uuid[i]);
    }
    MakeUnsignedChar(buffer_,offset,data.cmd_type);
    MakeUnsignedChar(buffer_,offset,data.cmd_res_data_len);
    switch (data.cmd_type)
    {
    case 0:
    {
 /* code */
        break;
    }
       
    case 1:
    {
        MakeETOPackage(buffer_,offset,data.eto_res);
        break;
    }
    case 2:
    {
        MakeDispatchPackage(buffer_,offset,data.dispatch_res);
        break;
    }
    case 3:
    {
        MakeGlobalRoutingPackage(buffer_,offset,data.global_routing_res);
        break;
    }
    case 4:
    {
        MakeOSAPackage(buffer_,offset,data.osa_res);
        break;
    }
    default:
        break;
    }
    MakeUnsignedChar(buffer_,offset,data.content_num);
    for(int i=0;i<data.content_num;i++)
    {
        MakeUnsignedChar(buffer_,offset,*(data.content.data()+i));
    }
    return buffer_;
}

void CmdDriveRes::MakeDispatchPackage(BYTE* buffer,DWORD& offset,RemoteDispatchResData data)
{
    MakeUnsignedChar(buffer,offset,data.do_flag);
    MakeUnsignedChar(buffer,offset,data.error_code);
    MakeUsignedLongLong(buffer,offset,data.expected_arrive_time);
}

void CmdDriveRes::MakeOSAPackage(BYTE* buffer,DWORD& offset,OSAResData data)
{
    MakeUnsignedChar(buffer,offset,data.do_flag);
    MakeUnsignedChar(buffer,offset,data.error_code);
}

void CmdDriveRes::MakeGlobalRoutingPackage(BYTE* buffer,DWORD& offset,GlobalRoutingResData data)
{
    MakeUnsignedChar(buffer,offset,data.do_flag);
    MakeUnsignedChar(buffer,offset,data.error_code);
}

void CmdDriveRes::MakeETOPackage(BYTE* buffer,DWORD& offset,EmergencyTakeoverResData data)
{
    MakeUnsignedChar(buffer,offset,data.do_flag);
    MakeUnsignedChar(buffer,offset,data.error_code);
}