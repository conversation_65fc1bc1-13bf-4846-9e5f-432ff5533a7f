#include "cmd_drive.h"

using namespace cvis_bcl;


CmdDrive::CmdDrive()
{
    msg_type = "C2V_CMD_DRIVE";
}

CmdDrive::~CmdDrive()
{

}

bool CmdDrive::ParseProtocal(BYTE* buffer,int length)
{
    DWORD offset=0;
    if(!header_.ParseHeader(buffer,offset,length))
    {
        return false;
    }
    if(header_.GetLength()+offset>length)
    {
        return false;
    }
    data_.msg_seq = GetHostUnsignedInt(buffer,offset);
    for(int i=0;i<8;i++)
    {
        data_.veh_id[i] = GetHostUnsignedChar(buffer,offset);
    }
    for(int i=0;i<36;i++)
    {
        data_.uuid[i] = GetHostUnsignedChar(buffer,offset);
    }
    data_.cmd_type = GetHostUnsignedChar(buffer,offset);
    data_.cmd_data_len = GetHostUnsignedChar(buffer,offset);
    switch(data_.cmd_type)
    {
        case 0:
        break;
        case 1:
        ParseETOData(buffer,offset,data_.eto_data);
        break;
        case 2:
        ParseDispatchData(buffer,offset,data_.dispatch_data);
        break;
        case 3:
        ParseGlobalRoutintgData(buffer,offset,data_.global_routing_data);
        break;
        case 4:
        ParseOSAData(buffer,offset,data_.osa_data);
        break;
        default:
        break;
    }
    return true;
}

void CmdDrive::ParseDispatchData(BYTE* buffer,DWORD& offset,RemoteDispatchData& data)
{
    data.dispatch_type = GetHostUnsignedChar(buffer,offset);
    data.target_position.longitude = GetLongitude(GetHostUnsignedInt(buffer,offset));
    data.target_position.latitude= GetLatitude(GetHostUnsignedInt(buffer,offset));
    data.target_position.elevation = GetElevation(GetHostUnsignedInt(buffer,offset));
    data.start_time = GetHostlonglong(buffer,offset);
    data.arrive_time = GetHostlonglong(buffer,offset);
}

void CmdDrive::ParseETOData(BYTE* buffer,DWORD& offset,EmergencyTakeoverData& data)
{
    data.take_charge_option = GetHostUnsignedChar(buffer,offset);
    data.emergency_level = GetHostUnsignedChar(buffer,offset);
}

void CmdDrive::ParseOSAData(BYTE* buffer,DWORD& offset,OSAData& data)
{
    data.way_points_num = GetHostUnsignedShort(buffer,offset);
    for(int i=0;i<data.way_points_num;i++)
    {
        WayPointLLE point;
        point.longitude = GetLongitude(GetHostUnsignedInt(buffer,offset));
        point.latitude = GetLatitude(GetHostUnsignedInt(buffer,offset));
        point.elevation = GetElevation(GetHostUnsignedInt(buffer,offset));
        data.way_points.push_back(point);
    }
}

void CmdDrive::ParseGlobalRoutintgData(BYTE* buffer,DWORD& offset,GlobalRoutingData& data)
{
    data.map_info_len = GetHostUnsignedChar(buffer,offset);
    data.map_info.assign((char*)(buffer+offset),data.map_info_len);
    offset += data.map_info_len;
    data.road_num = GetHostUnsignedChar(buffer,offset);
    for(int i=0;i<data.road_num;i++)
    {
        RoadSeqData road_seq;
        road_seq.road_name_length = GetHostUnsignedChar(buffer,offset);
        road_seq.road_name.assign((char*)(buffer+offset),road_seq.road_name_length);
        offset+=road_seq.road_name_length;
        data.road_seq.push_back(road_seq);
    }
}