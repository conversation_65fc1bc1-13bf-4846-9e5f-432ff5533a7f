/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 消息映射表
 *
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/
#pragma once
#include <unordered_map>
#include <string>
namespace cvis_bcl
{
    static std::unordered_map<int,std::string> topic_reflect_map=
    {
        {0x0c,"HEARTBEAT"},
        {0x0D,"HEARTBEAT_RES"},
        {0x15,"V2C_STATE_V1"},
        {0x16,"V2C_STATE_V2"},
        {0x17,"V2C_STATE_V3"},
        {0x18,"V2C_STATUS"},
        {0x19,"V2C_EVENT"},
        {0x1A,"V2C_DETECTION"},
        {0x1F,"C2V_CTL_REMOTE_DRIVING"},
        {0x20,"C2V_CTL_LOCAL_ROUTE"},
        {0x21,"C2V_CTL_VELOCITY"},
        {0x32,"C2V_TRAFFIC_INFO"},
        {0x34,"V2C_INH"},
        {0x35,"C2V_INH_RES"},
        {0x36,"V2C_FUNC_REQ"},
        {0x37,"C2V_FUNC_REQ_RES"},
        {0x38,"V2C_CFG_REQ"},
        {0x39,"C2V_CFG_REQ_RES"},
        {0x3A,"C2V_CFG_SYNC"},
        {0x3B,"V2C_CFG_SYNC_RES"},
        {0x3C,"C2V_ADVICE"},
        {0x3D,"V2C_ADVICE_RES"},
        {0x3E,"V2C_DATA_REQ"},
        {0x3F,"C2V_DATA_REQ_RES"},
        {0x40,"V2C_INTERACTION_REQ"},
        {0x41,"C2V_INTERACTION_REQ_RES"},
        {0x42,"C2V_INTERACTION_SYNC"},
        {0x43,"V2C_INTERACTION_SYNC_RES"},
        {0x44,"C2V_CMD_DRIVE"},
        {0x45,"V2C_CMD_DRIVE_RES"},
        {0x5c,"V2C_TEST"},
        {0x5D,"C2V_TEST_RES"},
        {0x5E,"C2V_TEST"},
        {0x5F,"V2C_TEST_RES"},
        {0x60,"V2C_STATE_RESEND"},
        {0x61,"C2V_STATE_RESEND_RES"},
        {0x62,"C2V_STATE_RESEND_CMD"},
        {0x63,"V2C_STATE_RESEND_CMD_RES"},

        //rcu
        {0x81, "RCU2CLOUD_STATUS"}
    };

    static std::unordered_map<std::string,int> datatype_reflect_map=
    {
        {"HEARTBEAT",0x0c},
        {"HEARTBEAT_RES",0x0D},
        {"V2C_STATE_V1",0x15},
        {"V2C_STATE_V2",0x16},
        {"V2C_STATE_V3",0x17},
        {"V2C_STATUS",0x18},
        {"V2C_EVENT",0x19},
        {"V2C_DETECTION",0x1A},
        {"C2V_CTL_REMOTE_DRIVING",0x1F},
        {"C2V_CTL_LOCAL_ROUTE",0x20},
        {"C2V_CTL_VELOCITY",0x21},
        {"C2V_TRAFFIC_INFO",0x32},
        {"V2C_INH",0x34},
        {"C2V_INH_RES",0x35},
        {"V2C_FUNC_REQ",0x36},
        {"C2V_FUNC_REQ_RES",0x37},
        {"V2C_CFG_REQ",0x38},
        {"C2V_CFG_REQ_RES",0x39},
        {"C2V_CFG_SYNC",0x3A},
        {"V2C_CFG_SYNC_RES",0x3B},
        {"C2V_ADVICE",0x3C},
        {"V2C_ADVICE_RES",0x3D},
        {"V2C_DATA_REQ",0x3E},
        {"C2V_DATA_REQ_RES",0x3F},
        {"V2C_INTERACTION_REQ",0x40},
        {"C2V_INTERACTION_REQ_RES",0x41},
        {"C2V_INTERACTION_SYNC",0x42},
        {"V2C_INTERACTION_SYNC_RES",0x43},
        {"C2V_CMD_DRIVE",0x44},
        {"V2C_CMD_DRIVE_RES",0x45},
        {"V2C_TEST",0x5c},
        {"C2V_TEST_RES",0x5D},
        {"C2V_TEST",0x5E},
        {"V2C_TEST_RES",0x5F},
        {"V2C_STATE_RESEND",0x60},
        {"C2V_STATE_RESEND_RES",0x61},
        {"C2V_STATE_RESEND_CMD",0x62},
        {"V2C_STATE_RESEND_CMD_RES",0x63},

        //rcu
        {"RCU2CLOUD_STATUS",0x81}
    };
}
